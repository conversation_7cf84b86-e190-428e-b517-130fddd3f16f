import { Component, OnInit, ChangeDetectionStrategy, OnDestroy } from '@angular/core';
import { Observable, Subject, combineLatest } from 'rxjs';
import { map, takeUntil, startWith } from 'rxjs/operators';
import { CharacterService, Character } from '../../../../core/services/character.service';

@Component({
  selector: 'app-character-list',
  templateUrl: './character-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CharacterListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  characters$ = this.characterService.characters$;
  loading$ = this.characterService.loading$;
  error$ = this.characterService.error$;
  
  searchTerm$ = new Subject<string>();
  sortBy$ = new Subject<string>();
  currentPage$ = new Subject<number>();
  
  filteredCharacters$: Observable<Character[]>;
  totalPages$: Observable<number> = new Observable<number>();
  
  readonly pageSize = 10;

  constructor(private characterService: CharacterService) {
    this.filteredCharacters$ = combineLatest([
      this.characters$,
      this.searchTerm$.pipe(startWith('')),
      this.sortBy$.pipe(startWith('name')),
      this.currentPage$.pipe(startWith(1))
    ]).pipe(
      map(([characters, search, sort, page]) => {
        let filtered = this.filterCharacters(characters, search);
        filtered = this.sortCharacters(filtered, sort);
        return this.paginateCharacters(filtered, page);
      })
    );

    this.totalPages$ = combineLatest([
      this.characters$,
      this.searchTerm$.pipe(startWith(''))
    ]).pipe(
      map(([characters, search]) => {
        const filtered = this.filterCharacters(characters, search);
        return Math.ceil(filtered.length / this.pageSize);
      })
    );
  }

  ngOnInit() {
    this.characterService.loadCharacters()
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSearch(term: string) {
    this.searchTerm$.next(term);
    this.currentPage$.next(1);
  }

  onSort(field: string) {
    this.sortBy$.next(field);
  }

  onPageChange(page: number) {
    this.currentPage$.next(page);
  }

  trackByFn(_: number, character: Character): number {
    return character.id;
  }

  private filterCharacters(characters: Character[], search: string): Character[] {
    if (!search) return characters;
    return characters.filter(char => 
      char.name.toLowerCase().includes(search.toLowerCase()) ||
      char.species.toLowerCase().includes(search.toLowerCase())
    );
  }

  private sortCharacters(characters: Character[], sortBy: string): Character[] {
    return [...characters].sort((a, b) => {
      const aValue = a[sortBy as keyof Character];
      const bValue = b[sortBy as keyof Character];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return aValue.localeCompare(bValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return aValue - bValue;
      }

      return String(aValue).localeCompare(String(bValue));
    });
  }

  private paginateCharacters(characters: Character[], page: number): Character[] {
    const start = (page - 1) * this.pageSize;
    return characters.slice(start, start + this.pageSize);
  }
}
import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { Character } from '../../../../core/services/character.service';

@Component({
  selector: 'app-character-card',
  template: `
    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
      <!-- Character Image -->
      <div class="h-48 bg-gray-200 flex items-center justify-center">
        <img
          *ngIf="character.images?.main; else noImage"
          [src]="character.images.main"
          [alt]="getFullName()"
          class="h-full w-full object-cover"
          (error)="onImageError($event)"
        />
        <ng-template #noImage>
          <div class="text-gray-400 text-center">
            <i class="fas fa-user text-4xl mb-2"></i>
            <p>No Image</p>
          </div>
        </ng-template>
      </div>

      <!-- Character Info -->
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-3">{{ getFullName() }}</h3>
        <div class="space-y-2 text-gray-600">
          <p><span class="font-medium">Yaş:</span> {{ character.age }}</p>
          <p><span class="font-medium">Cinsiyet:</span> {{ character.gender }}</p>
          <p><span class="font-medium">Tür:</span> {{ character.species }}</p>
          <p><span class="font-medium">Meslek:</span> {{ character.occupation }}</p>
          <p><span class="font-medium">Gezegen:</span> {{ character.homePlanet }}</p>
        </div>

        <!-- Random Saying -->
        <div *ngIf="character.sayings && character.sayings.length > 0" class="mt-4 p-3 bg-gray-50 rounded-lg">
          <p class="text-sm italic text-gray-700">"{{ getRandomSaying() }}"</p>
        </div>
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CharacterCardComponent {
  @Input() character!: Character;

  getFullName(): string {
    if (!this.character?.name) return 'Unknown';
    const { first, middle, last } = this.character.name;
    return [first, middle, last].filter(Boolean).join(' ');
  }

  getRandomSaying(): string {
    if (!this.character?.sayings?.length) return '';
    const randomIndex = Math.floor(Math.random() * this.character.sayings.length);
    return this.character.sayings[randomIndex];
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    img.style.display = 'none';
  }
}
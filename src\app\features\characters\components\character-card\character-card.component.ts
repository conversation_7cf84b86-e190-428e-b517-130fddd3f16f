import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { Character } from '../../../../core/services/character.service';

@Component({
  selector: 'app-character-card',
  template: `
    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
      <h3 class="text-xl font-bold text-gray-800 mb-2">{{ character.name }}</h3>
      <div class="space-y-2 text-gray-600">
        <p><span class="font-medium">Tür:</span> {{ character.species }}</p>
        <p><span class="font-medium">Meslek:</span> {{ character.profession }}</p>
        <p><span class="font-medium">Gezegen:</span> {{ character.planet }}</p>
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CharacterCardComponent {
  @Input() character!: Character;
}
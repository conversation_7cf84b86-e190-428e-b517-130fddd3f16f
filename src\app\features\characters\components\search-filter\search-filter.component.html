<div class="search-filter-container p-4 bg-white rounded-lg shadow-md mb-6">
  <div class="flex flex-col md:flex-row gap-4">
    <!-- Search Input -->
    <div class="flex-1">
      <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
        Search Characters
      </label>
      <input
        id="search"
        type="text"
        [(ngModel)]="searchTerm"
        (input)="onSearchChange($event)"
        placeholder="Search by name..."
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    </div>

    <!-- Sort Dropdown -->
    <div class="md:w-48">
      <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">
        Sort By
      </label>
      <select
        id="sort"
        [(ngModel)]="sortBy"
        (change)="onSortChange($event)"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <option value="name">Name</option>
        <option value="status">Status</option>
        <option value="species">Species</option>
        <option value="gender">Gender</option>
      </select>
    </div>
  </div>
</div>
